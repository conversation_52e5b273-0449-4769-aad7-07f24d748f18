{"name": "rashmi-metaliks-website", "version": "1.0.0", "description": "Official website for Rashmi Metaliks", "scripts": {"frontend": "cd frontend && npm run dev", "backend": "cd backend && npm run dev", "dev": "concurrently \"npm run frontend\" \"npm run backend\"", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "build": "cd frontend && npm run build && cd ../backend && npm run build", "build:production": "NODE_ENV=production npm run build", "start": "cd backend && npm start", "start:production": "cd backend && NODE_ENV=production npm start", "pm2:start": "pm2 start ecosystem.config.js --env production", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js --env production", "pm2:logs": "pm2 logs rashmi-backend", "deploy": "chmod +x deployment/deploy.sh && deployment/deploy.sh", "health": "curl -f http://localhost:3001/health || echo 'Health check failed'"}, "devDependencies": {"@rollup/plugin-wasm": "^6.2.2", "concurrently": "^8.2.2"}, "dependencies": {"@types/next": "^8.0.7", "@types/node": "^22.15.3", "@types/nodemailer": "^6.4.17", "chart.js": "^4.4.9", "next": "^15.3.1", "nodemailer": "^6.10.1", "react-chartjs-2": "^5.3.0", "vite": "^6.3.4"}}