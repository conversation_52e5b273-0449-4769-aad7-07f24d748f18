#!/usr/bin/env node

/**
 * Test script to verify CMS endpoints are working correctly
 * This script tests the production Strapi endpoints
 */

const BASE_URL = 'https://rashmimetaliks.com/strapi';

async function testEndpoint(endpoint, description) {
  console.log(`\n🔍 Testing ${description}...`);
  console.log(`URL: ${BASE_URL}${endpoint}`);
  
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`);
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Success! Found ${data.data ? data.data.length : 'N/A'} items`);
      
      // Log first item for debugging
      if (data.data && data.data.length > 0) {
        console.log(`First item preview:`, {
          id: data.data[0].id,
          attributes: Object.keys(data.data[0].attributes || {})
        });
      }
    } else {
      console.log(`❌ Failed: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.log(`Error details: ${errorText.substring(0, 200)}...`);
    }
  } catch (error) {
    console.log(`❌ Network Error: ${error.message}`);
  }
}

async function runTests() {
  console.log('🚀 Testing CMS Endpoints');
  console.log('========================');
  
  // Test all the endpoints used by the application
  await testEndpoint('/api/news-and-updates-panel?populate=*', 'News and Updates Panel');
  await testEndpoint('/api/certificates?populate=*', 'Certificates');
  await testEndpoint('/api/brochures?populate=*', 'Brochures');
  await testEndpoint('/api/csr?populate=*', 'CSR Content');
  
  console.log('\n🏁 Testing completed!');
  console.log('\nIf any endpoints failed, check:');
  console.log('1. Strapi server is running at https://rashmimetaliks.com/strapi');
  console.log('2. Content types are published and accessible');
  console.log('3. API permissions are configured correctly');
}

// Run the tests
runTests().catch(console.error);
