/* Add to your global CSS */
@layer components {
  .legal-content {
    @apply text-base leading-relaxed;
  }
  
  .legal-content h2 { 
    @apply font-serif text-2xl sm:text-3xl text-red-600 dark:text-amber-400 mb-6 mt-12; 
  }
  
  .legal-content h3 { 
    @apply font-semibold text-slate-800 dark:text-slate-200 text-xl mb-4; 
  }
  
  .legal-content p { 
    @apply mb-4 leading-relaxed; 
  }
  
  .legal-content ul { 
    @apply list-disc pl-6 mb-6 space-y-2; 
  }
  
  .legal-content a { 
    @apply text-red-600 dark:text-amber-400 hover:underline; 
  }
  
  .legal-content strong { 
    @apply text-red-600 dark:text-amber-400 font-medium; 
  }
  
  @keyframes fade-in {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .animate-fade-in {
    animation: fade-in 0.5s ease-out forwards;
  }
  
  /* Additional animations used in DiPipes and other components */
  @keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes gradientFlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
  
  .animated-gradient {
    background-size: 200% 200%;
    background-image: linear-gradient(to right, #E53935, #FF5252, #E53935);
    animation: gradientFlow 8s ease infinite;
  }
  
  /* Geometric pattern background */
  .geometric-pattern {
    background-image: radial-gradient(rgba(235, 89, 81, 0.1) 2px, transparent 2px), 
                      radial-gradient(rgba(235, 89, 81, 0.07) 2px, transparent 2px);
    background-size: 40px 40px;
    background-position: 0 0, 20px 20px;
  }
  
  /* Grid pattern background */
  .bg-grid-pattern {
    background-size: 50px 50px;
    background-image: 
      linear-gradient(to right, rgba(229, 57, 53, 0.03) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(229, 57, 53, 0.03) 1px, transparent 1px);
  }
  
  /* Gradient border effect */
  .gradient-border {
    position: relative;
  }
  
  .gradient-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(235, 89, 81, 0.7), transparent);
  }
  
  /* Perspective for 3D effects */
  .perspective-container {
    perspective: 1000px;
    transform-style: preserve-3d;
  }
}

/* Print styles */
@media print {
  .no-print { display: none !important; }
  body { font-size: 12pt; }
  .legal-content { max-width: 100% !important; }
  a::after { content: " (" attr(href) ")"; }
} 