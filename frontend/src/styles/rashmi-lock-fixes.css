/* Define animation keyframes directly instead of using @import */

/* Spotlight effect custom styles */
.spotlight-effect {
  position: absolute;
  pointer-events: none;
  inset: 0;
  z-index: 0;
  background: radial-gradient(
    600px circle at var(--x) var(--y),
    rgba(229, 57, 53, 0.15),
    transparent 40%
  );
}

/* Fix for shimmer animation */
@keyframes shimmer {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 2s infinite;
} 