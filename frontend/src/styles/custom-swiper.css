/* Custom styling for Swiper's navigation buttons */
.swiper-button-next,
.swiper-button-prev {
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  color: #eb5951 !important; /* Rashmi red color */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
}

/* Show buttons on hover */
.group:hover .swiper-button-next,
.group:hover .swiper-button-prev {
  opacity: 1;
}

.swiper-button-next:hover,
.swiper-button-prev:hover {
  background-color: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
}

/* Adjust the default arrow size */
.swiper-button-next:after,
.swiper-button-prev:after {
  font-size: 18px !important;
  font-weight: bold;
}

/* Ensure proper positioning */
.swiper-button-prev {
  left: 10px;
}

.swiper-button-next {
  right: 10px;
}

/* Add pulse animation on initial load to draw attention */
@keyframes pulse {
  0% { transform: scale(1); opacity: 0.7; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 0.7; }
}

.swiper-container:hover .swiper-button-next,
.swiper-container:hover .swiper-button-prev {
  animation: pulse 2s infinite;
}

/* Stop animation on hover */
.swiper-button-next:hover,
.swiper-button-prev:hover {
  animation: none;
} 