@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}
.animate-float {
  animation: float 3s ease-in-out infinite;
}

@keyframes gradientFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animated-gradient {
  background-size: 200% 200%;
  background-image: linear-gradient(to right, #E53935, #FF5252, #E53935);
  animation: gradientFlow 8s ease infinite;
}

@keyframes shine {
  from {
    background-position: -100px;
  }
  to {
    background-position: 200px;
  }
}

.animate-shine {
  animation: shine 2s linear infinite;
  background-size: 200px 100%;
  background-repeat: no-repeat;
}

.geometric-pattern {
  background-image: radial-gradient(rgba(235, 89, 81, 0.1) 2px, transparent 2px), 
                    radial-gradient(rgba(235, 89, 81, 0.07) 2px, transparent 2px);
  background-size: 40px 40px;
  background-position: 0 0, 20px 20px;
}

.bg-grid-pattern {
  background-size: 50px 50px;
  background-image: 
    linear-gradient(to right, rgba(229, 57, 53, 0.03) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(229, 57, 53, 0.03) 1px, transparent 1px);
}

.perspective-3d {
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* Shimmer effect for buttons */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Tailwind-style pulsing */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Modified Ant Design table styles for spec table */
.spec-table .ant-table-thead > tr > th {
  background: rgba(229, 57, 53, 0.05);
  color: #E53935;
  font-weight: 500;
}

.spec-table .ant-table-tbody > tr:hover > td {
  background: rgba(229, 57, 53, 0.03);
}

.spec-table .ant-table-cell {
  transition: all 0.3s ease;
}

/* Hover effects for cards */
.hover-card {
  transition: all 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px -10px rgba(0, 0, 0, 0.15);
  border-color: rgba(229, 57, 53, 0.3);
}

/* Spotlight effect custom styles */
.spotlight {
  position: absolute;
  pointer-events: none;
  inset: 0;
  z-index: 0;
  background: radial-gradient(
    600px circle at var(--x) var(--y),
    rgba(229, 57, 53, 0.15),
    transparent 40%
  );
}

/* Typewriter effect */
.typewriter {
  border-right: 2px solid #E53935;
  overflow: hidden;
  white-space: nowrap;
  margin: 0 auto;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0 }
  to { width: 100% }
}

@keyframes blink-caret {
  from, to { border-color: transparent }
  50% { border-color: #E53935 }
} 