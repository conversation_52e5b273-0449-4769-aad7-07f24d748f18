{"name": "rashmi-metaliks-backend", "version": "1.0.0", "description": "Backend API for Rashmi Metaliks Website", "main": "src/index.js", "scripts": {"start": "node dist/index.js", "dev": "npm run verify-env && nodemon --exec ts-node src/index.ts", "build": "tsc", "test": "jest", "verify-env": "node scripts/verify-env.js", "prestart": "npm run verify-env"}, "dependencies": {"@types/express-session": "^1.18.2", "@types/form-data": "^2.2.1", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "form-data": "^4.0.2", "helmet": "^7.1.0", "joi": "^17.13.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "nodemailer": "^6.9.14", "uuid": "^10.0.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^20.11.5", "chalk": "^4.1.2", "nodemon": "^3.0.3", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}