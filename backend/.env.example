# ========================================
# RASHMI METALIKS BACKEND CONFIGURATION
# ========================================
#
# SECURITY WARNING:
# - Never commit this file with real values to version control
# - Keep all sensitive tokens and keys secure
# - Rotate tokens regularly
# - Use different tokens for development and production
#
# Setup Instructions:
# 1. Copy this file to .env
# 2. Replace all placeholder values with your actual credentials
# 3. Ensure .env is in your .gitignore file
# ========================================

# Server Configuration
PORT=3001
NODE_ENV=development

# Supabase Configuration (Service Role)
# Get these from your Supabase project settings
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_SERVICE_KEY=your_supabase_service_role_key_here

# CMS Configuration (Strapi)
# IMPORTANT: Use a secure API token, not the default one
CMS_API_URL=http://localhost:1337
CMS_API_TOKEN=your_secure_strapi_api_token_here
CMS_NEWS_API_ID=news-and-updates
CMS_NEWS_PANEL_API_ID=news-and-updates-panel

# CORS Configuration
CORS_ORIGIN=http://localhost:8080

# reCAPTCHA Configuration
# Get your keys from Google reCAPTCHA console
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key_here

# Email Configuration (SMTP)
# Configure your email service provider settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_specific_password_here
