# Backend Configuration
NODE_ENV=production
PORT=3001

# CORS Configuration
CORS_ORIGIN=https://your-domain.com

# CMS/Strapi Configuration
CMS_API_URL=http://localhost:1337
CMS_API_TOKEN=your_strapi_api_token_here
CMS_NEWS_API_ID=news-and-updates
CMS_NEWS_PANEL_API_ID=news-and-updates-panel

# Email Configuration (if using nodemailer)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_KEY=your_supabase_service_role_key

# Other API Keys
RECAPTCHA_SECRET_KEY=your_recaptcha_secret_key

# Frontend Environment Variables (for Vercel)
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
VITE_APP_NAME=Rashmi Metaliks
